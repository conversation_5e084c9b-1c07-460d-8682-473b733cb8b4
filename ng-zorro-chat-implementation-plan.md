# ng-zorro-antd Chat Interface Implementation Plan

## Overview
This document outlines the implementation plan for recreating the Agrimi AI chat interface using ng-zorro-antd components. The design features a sidebar-content layout with conversation history, main chat area, and input controls.

## UI Design Analysis

### Key Visual Elements
1. **Main Layout**: Sidebar-content layout with purple header
2. **Header**: Purple background with "Agrimi AI" branding and window controls
3. **Sidebar**: Left navigation panel with conversation history and menu items
4. **Main Content**: Chat area with greeting message and suggested questions
5. **Input Area**: Text input with send button at the bottom
6. **Suggested Actions**: Pill-shaped buttons with questions

## Component Mapping Strategy

### 1. Main Layout Structure

**Primary Container**: `nz-layout`
- **Header**: `nz-header` (purple header with branding)
- **Content Wrapper**: `nz-layout` (nested layout)
  - **Sidebar**: `nz-sider` (collapsible left navigation)
  - **Main Content**: `nz-content` (chat area)

```html
<nz-layout class="main-layout">
  <nz-header class="app-header">
    <!-- Header content -->
  </nz-header>
  <nz-layout class="content-wrapper">
    <nz-sider class="sidebar">
      <!-- Sidebar content -->
    </nz-sider>
    <nz-content class="main-content">
      <!-- Chat content -->
    </nz-content>
  </nz-layout>
</nz-layout>
```

### 2. Header Components

**Component**: `nz-header`
- **Configuration**: Custom purple background styling
- **Content**: 
  - Title/Branding: Simple text/icon combination
  - Window Controls: `nz-button` with `nzType="text"` and `nzShape="circle"`

**Properties**:
- Custom CSS class for purple background
- Flex layout for title and controls alignment

### 3. Sidebar Components

**Component**: `nz-sider`
- **Configuration**:
  - `nzWidth="280"`
  - `nzCollapsible="true"`
  - `nzTheme="light"`
  - `nzBreakpoint="lg"`

**Content Structure**:
1. **New Conversation Button**: `nz-button`
   - `nzType="default"`
   - `nzBlock="true"`
   - Plus icon prefix

2. **Conversation History**: `nz-menu`
   - `nzMode="inline"`
   - `nzTheme="light"`
   - Section headers with custom styling
   - `nz-menu-item` for each conversation

3. **Bottom Navigation**: `nz-menu`
   - `nz-menu-item` for settings, guides, contact
   - Icons with text labels

### 4. Main Chat Area Components

**Component**: `nz-content`

**Content Structure**:
1. **Welcome Section**:
   - Greeting text with custom typography
   - Subtitle with custom styling

2. **Suggested Questions**: `nz-flex`
   - `nzGap="middle"`
   - `nzWrap="wrap"`
   - `nzJustify="flex-start"`
   - `nzAlign="center"`
   - Question pills as `nz-button` with custom styling

3. **Input Area**: Fixed positioned container
   - `nz-input-group` with suffix button
   - `nz-input` for text input
   - `nz-button` for send action

### 5. Detailed Component Specifications

#### Sidebar Configuration
```typescript
// nz-sider properties
nzWidth = "280"
nzCollapsible = true
nzTheme = "light"
nzBreakpoint = "lg"
nzCollapsedWidth = 64
```

#### Menu Configuration
```typescript
// nz-menu properties
nzMode = "inline"
nzTheme = "light"
nzSelectable = true
```

#### Input Group Configuration
```typescript
// nz-input-group properties
nzSize = "large"
// nz-input properties
placeholder = "Попитайте Agrimi AI"
```

#### Button Configurations
```typescript
// New conversation button
nzType = "default"
nzBlock = true

// Suggested question buttons
nzType = "default"
// Custom CSS for pill shape

// Send button
nzType = "primary"
nzShape = "circle"
```

#### Flex Layout Configuration
```typescript
// Suggested questions container
nzJustify = "flex-start"
nzAlign = "center"
nzGap = "middle"
nzWrap = "wrap"
```

## Custom Styling Requirements

### CSS Classes Needed

1. **Header Styling**:
```css
.app-header {
  background-color: #8B5CF6; /* Purple brand color */
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}
```

2. **Suggested Question Pills**:
```css
.question-pill {
  border-radius: 20px;
  border: 1px solid #e0e0e0;
  background: white;
  padding: 8px 16px;
}
```

3. **Input Area**:
```css
.input-area {
  position: fixed;
  bottom: 0;
  left: 280px; /* Sidebar width */
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
}
```

4. **Conversation Items**:
```css
.conversation-item {
  padding: 8px 16px;
  border-radius: 8px;
  margin: 2px 8px;
}

.conversation-item:hover {
  background-color: #f5f5f5;
}
```

## Icons Required

### Icon Mapping
- **Plus icon**: `plus` (new conversation)
- **Sparkle icons**: `star` or custom sparkle (suggested questions)
- **Send icon**: `send` or `arrow-right` (input submit)
- **Chat icons**: `message` (conversation history)
- **Settings icon**: `setting` (bottom menu)
- **Guide icon**: `question-circle` (bottom menu)
- **Contact icon**: `phone` (bottom menu)

### Icon Implementation
```html
<!-- Example icon usage -->
<nz-icon nzType="plus" nzTheme="outline"></nz-icon>
<nz-icon nzType="star" nzTheme="outline"></nz-icon>
<nz-icon nzType="send" nzTheme="outline"></nz-icon>
```

## Responsive Design Considerations

### Breakpoint Strategy
1. **Desktop (≥992px)**: Full sidebar visible
2. **Tablet (768px-991px)**: Collapsible sidebar
3. **Mobile (<768px)**: Hidden sidebar with overlay

### Implementation
```typescript
// Responsive sidebar configuration
@Component({
  template: `
    <nz-sider 
      [nzCollapsed]="isCollapsed"
      [nzBreakpoint]="'lg'"
      (nzCollapsedChange)="onCollapsedChange($event)">
    </nz-sider>
  `
})
```

### Mobile Adaptations
- Input area spans full width on mobile
- Sidebar becomes overlay drawer
- Suggested questions stack vertically
- Header adjusts for mobile viewport

## Implementation Priority

### Phase 1: Core Layout
1. Set up main `nz-layout` structure
2. Implement `nz-header` with basic styling
3. Configure `nz-sider` with basic menu
4. Set up `nz-content` area

### Phase 2: Sidebar Functionality
1. Implement conversation history with `nz-menu`
2. Add new conversation button
3. Create bottom navigation menu
4. Add hover and selection states

### Phase 3: Chat Interface
1. Create welcome message section
2. Implement suggested questions with `nz-flex`
3. Build input area with `nz-input-group`
4. Add send button functionality

### Phase 4: Styling & Polish
1. Apply custom CSS for brand colors
2. Implement pill-shaped buttons
3. Add responsive behaviors
4. Fine-tune spacing and typography

### Phase 5: Interactions
1. Add click handlers for menu items
2. Implement input submission
3. Add keyboard shortcuts
4. Handle responsive state changes

## Dependencies

### Required ng-zorro-antd Modules
```typescript
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzFlexModule } from 'ng-zorro-antd/flex';
```

### Additional Considerations
- Ensure proper TypeScript types for component properties
- Implement proper accessibility attributes
- Consider internationalization for text content
- Plan for theme customization capabilities

This implementation plan provides a comprehensive roadmap for recreating the chat interface using ng-zorro-antd components while maintaining design fidelity and ensuring responsive behavior.
